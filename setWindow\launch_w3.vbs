' ============================================================================
' Window Management Tool - Invisible Launcher
' ============================================================================
' Author: Liu Lifu  
' Version: 2025-01-11-16:15:00
' Purpose: Launch w3.py with absolutely no visible windows
' Usage: Double-click this file or create shortcut to it
' ============================================================================

On Error Resume Next

' Create required objects
Set ws = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Define absolute paths
Dim pythonPath, w3Path
pythonPath = "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
w3Path = "C:\Users\<USER>\OneDrive - BeiGene\Gitlab\script01\setWindow\w3.py"

' Validate Python interpreter exists
If Not fso.FileExists(pythonPath) Then
    MsgBox "Window Management Tool Error:" & vbCrLf & vbCrLf & _
           "Python interpreter not found at:" & vbCrLf & pythonPath & vbCrLf & vbCrLf & _
           "Please check your Python installation path.", _
           vbCritical + vbSystemModal, "Python Not Found"
    WScript.Quit 1
End If

' Validate w3.py exists
If Not fso.FileExists(w3Path) Then
    MsgBox "Window Management Tool Error:" & vbCrLf & vbCrLf & _
           "w3.py not found at:" & vbCrLf & w3Path & vbCrLf & vbCrLf & _
           "Please check the script location.", _
           vbCritical + vbSystemModal, "Script Not Found"
    WScript.Quit 1
End If

' Create command with absolute paths
Dim command
command = """" & pythonPath & """ """ & w3Path & """"

' Launch the Python script
Err.Clear
ws.Run command, 0, False

' Check if launch was successful
If Err.Number <> 0 Then
    MsgBox "Window Management Tool Error:" & vbCrLf & vbCrLf & _
           "Could not launch Python script." & vbCrLf & vbCrLf & _
           "Error: " & Err.Description & vbCrLf & vbCrLf & _
           "Please ensure:" & vbCrLf & _
           "• PyQt5 and pygetwindow are installed" & vbCrLf & _
           "• Python modules are accessible" & vbCrLf & vbCrLf & _
           "Install missing modules with:" & vbCrLf & _
           "pip install PyQt5 pygetwindow", _
           vbCritical + vbSystemModal, "Launch Error"
    WScript.Quit 1
End If

' Cleanup
Set ws = Nothing
Set fso = Nothing
